import time
import subprocess
import json
import pyautogui
import sys
import io
from image_recognizer import (
    capture_screen,
    get_puzzle_state,
    load_reference_tiles,
    format_state_for_solver,
)
from mouse_automator import automate_solution

def get_puzzle_region_from_user():
    """通过用户交互获取拼图在屏幕上的区域"""
    input("请将鼠标移动到拼图区域的左上角，然后按 Enter...")
    top_left = pyautogui.position()
    print(f"已记录左上角坐标: {top_left}")

    input("现在，请将鼠标移动到拼图区域的右下角，然后按 Enter...")
    bottom_right = pyautogui.position()
    print(f"已记录右下角坐标: {bottom_right}")

    left = top_left.x
    top = top_left.y
    width = bottom_right.x - left
    height = bottom_right.y - top

    if width <= 0 or height <= 0:
        print("错误：无效的区域。右下角坐标必须大于左上角坐标。")
        return None

    return (left, top, width, height)

def configure_io_streams():
    """Configure stdin and stdout for UTF-8"""
    sys.stdin = io.TextIOWrapper(sys.stdin.buffer, encoding='utf-8')
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

def main():
    """主协调函数"""
    configure_io_streams()
    # 1. 获取用户输入
    puzzle_id = input("请输入您要解决的拼图 ID (例如, '11', '21'): ")
    
    try:
        with open('puzzle_metadata.json', 'r', encoding='utf-8') as f:
            puzzles = json.load(f)
        puzzle_info = next((p for p in puzzles if p["puzzle_id"] == puzzle_id), None)
        if not puzzle_info:
            print(f"错误: 未在 puzzle_metadata.json 中找到 ID 为 {puzzle_id} 的拼图。")
            return
        puzzle_size = puzzle_info["size"]
    except FileNotFoundError:
        print("错误: puzzle_metadata.json 未找到。")
        return

    puzzle_region = get_puzzle_region_from_user()
    if not puzzle_region:
        return
        
    print("准备在5秒后开始截图和识别，请确保拼图界面可见...")
    time.sleep(5)

    # 2. 识别当前状态
    print("正在截取屏幕并识别拼图状态...")
    screenshot = capture_screen(region=puzzle_region)
    
    reference_tiles = load_reference_tiles(puzzle_id)
    if not reference_tiles:
        return
        
    initial_state_matrix = get_puzzle_state(screenshot, puzzle_size, reference_tiles)
    if not initial_state_matrix:
        print("错误: 无法识别拼图状态。")
        return
    
    print(f"识别出的初始状态: {initial_state_matrix}")
    initial_state_str = format_state_for_solver(initial_state_matrix)
    print(f"格式化后的状态: {initial_state_str}")

    # 3. 生成解法
    print("正在调用解法器生成解法...")
    solution_filename = f"solution_{puzzle_id}_auto.json"
    try:
        subprocess.run(
            ['python', 'solver.py', '--puzzle-id', puzzle_id, '--initial-state', initial_state_str],
            check=True, capture_output=True, text=True, encoding='utf-8'
        )
        # solver.py now saves to solution_[puzzle_id].json, we need to read that
        solver_output_filename = f"solution_{puzzle_id}.json"
        with open(solver_output_filename, 'r', encoding='utf-8') as f:
            solution_data = json.load(f)
        
        if "error" in solution_data or not solution_data.get("solution_path"):
            print(f"错误: 未能找到解法。解法器返回: {solution_data.get('error', '未知错误')}")
            return
            
        solution_path = solution_data["solution_path"]
        print(f"成功生成解法，共 {solution_data['steps']} 步。")

    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"错误: 解决拼图失败 - {e}")
        if hasattr(e, 'stderr') and e.stderr:
            print(f"解法器错误输出:\n{e.stderr}")
        return

    # 4. 执行解法
    print("准备在5秒后开始自动操作鼠标，请不要移动鼠标...")
    time.sleep(5)
    automate_solution(puzzle_region, puzzle_size, initial_state_matrix, solution_path)
    
    print("拼图已自动完成！")

if __name__ == "__main__":
    main()