import random
import arg<PERSON><PERSON>

def get_scrambled_state(size=3, moves=50):
    """
    Generates a solvable scrambled state for an size x size puzzle.

    Args:
        size (int): The dimension of the puzzle.
        moves (int): The number of random moves to apply.

    Returns:
        list: A scrambled but solvable initial state.
    """
    # Target state
    state = list(range(1, size*size)) + [0]
    
    for _ in range(moves):
        zero_index = state.index(0)
        row, col = zero_index // size, zero_index % size
        
        possible_moves = []
        # Up
        if row > 0:
            possible_moves.append(zero_index - size)
        # Down
        if row < size - 1:
            possible_moves.append(zero_index + size)
        # Left
        if col > 0:
            possible_moves.append(zero_index - 1)
        # Right
        if col < size - 1:
            possible_moves.append(zero_index + 1)
            
        move_to = random.choice(possible_moves)
        state[zero_index], state[move_to] = state[move_to], state[zero_index]
        
    return state

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate a scrambled state for a sliding puzzle.")
    parser.add_argument("--size", type=int, default=3, help="The size of the puzzle (e.g., 3 for a 3x3 puzzle).")
    args = parser.parse_args()

    scrambled_state = get_scrambled_state(size=args.size, moves=args.size * args.size * 5)
    print(",".join(map(str, scrambled_state)))