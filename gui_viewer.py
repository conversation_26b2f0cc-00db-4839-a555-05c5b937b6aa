import json
import pygame
import sys
import time
import argparse

def load_data(puzzle_id):
    """加载解法和拼图元数据"""
    solution_file = f'solution_{puzzle_id}.json'
    try:
        with open(solution_file, 'r', encoding='utf-8') as f:
            solution_data = json.load(f)
        with open('puzzle_metadata.json', 'r', encoding='utf-8') as f:
            puzzles_metadata = json.load(f)
    except FileNotFoundError as e:
        print(f"错误: {e.filename} 未找到。请确保文件存在于正确的目录中。")
        sys.exit(1)

    puzzle_id = str(solution_data.get("puzzle_id"))
    solution_path = solution_data.get("solution_path")

    puzzle_info = None
    for puzzle in puzzles_metadata:
        if puzzle['puzzle_id'] == puzzle_id:
            puzzle_info = puzzle
            break
    if not puzzle_info:
        print(f"错误: 在 puzzle_metadata.json 中未找到 ID 为 {puzzle_id} 的拼图。")
        sys.exit(1)
        
    return puzzle_info, solution_path

def load_tiles(puzzle_info):
    """加载所有拼图图块"""
    tiles = {}
    for tile_data in puzzle_info['pieces']:
        tile_id = tile_data['piece_number']
        tile_path = tile_data['path']
        try:
            tiles[tile_id] = pygame.image.load(tile_path)
        except pygame.error as e:
            print(f"错误: 无法加载图块图像 {tile_path}。")
            print(e)
            sys.exit(1)
    return tiles

def get_initial_state(target_state, solution_path):
    """从目标状态和解法路径反推出初始状态"""
    state = [row[:] for row in target_state]
    rows, cols = len(state), len(state[0])
    
    # 创建一个从移动方向到偏移量的映射
    moves = {'up': (1, 0), 'down': (-1, 0), 'left': (0, 1), 'right': (0, -1)}

    # 逆向遍历解法路径
    for move in reversed(solution_path):
        # 找到空白块（0）的位置
        zero_pos = None
        for r in range(rows):
            for c in range(cols):
                if state[r][c] == 0:
                    zero_pos = (r, c)
                    break
            if zero_pos:
                break
        
        # 计算要移动的图块的位置
        dr, dc = moves[move]
        tile_r, tile_c = zero_pos[0] + dr, zero_pos[1] + dc

        # 交换图块和空白块
        state[zero_pos[0]][zero_pos[1]], state[tile_r][tile_c] = \
            state[tile_r][tile_c], state[zero_pos[0]][zero_pos[1]]
            
    return state

def draw_puzzle(screen, state, tiles, rows, cols, tile_width, tile_height):
    """在屏幕上绘制拼图"""
    screen.fill((255, 255, 255))
    for r in range(rows):
        for c in range(cols):
            tile_id = state[r][c]
            if tile_id != 0:
                screen.blit(tiles[tile_id], (c * tile_width, r * tile_height))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='可视化拼图解法。')
    parser.add_argument('--puzzle-id', type=str, required=True, help='要可视化的拼图ID。')
    args = parser.parse_args()

    puzzle_info, solution_path = load_data(args.puzzle_id)

    rows = puzzle_info['size']
    cols = puzzle_info['size']
    # Dynamically determine tile size by loading the first piece
    first_piece_path = puzzle_info['pieces'][0]['path']
    first_piece_image = pygame.image.load(first_piece_path)
    tile_width = first_piece_image.get_width()
    tile_height = first_piece_image.get_height()

    screen_width = cols * tile_width
    screen_height = rows * tile_height

    pygame.init()
    screen = pygame.display.set_mode((screen_width, screen_height))
    pygame.display.set_caption(f"拼图 {puzzle_info['puzzle_id']} 求解过程")

    tiles = load_tiles(puzzle_info)

    # 获取目标状态
    # Dynamically generate the target state
    size = puzzle_info['size']
    target_state_flat = list(range(1, size * size)) + [0]
    target_state = [target_state_flat[i:i+cols] for i in range(0, len(target_state_flat), cols)]

    # 从解法路径反推初始状态
    initial_state = get_initial_state(target_state, solution_path)
    current_state = [row[:] for row in initial_state]

    # 游戏主循环
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

        # 绘制初始状态
        draw_puzzle(screen, current_state, tiles, rows, cols, tile_width, tile_height)
        pygame.display.flip()
        time.sleep(1) # 显示初始状态1秒

        # 按步骤展示求解过程
        for move in solution_path:
            # 检查退出事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()

            # 找到空白块
            zero_pos = None
            for r in range(rows):
                for c in range(cols):
                    if current_state[r][c] == 0:
                        zero_pos = (r, c)
                        break
                if zero_pos:
                    break
            
            # 计算要移动的图块的位置
            moves = {'up': (-1, 0), 'down': (1, 0), 'left': (0, -1), 'right': (0, 1)}
            dr, dc = moves[move]
            tile_r, tile_c = zero_pos[0] + dr, zero_pos[1] + dc

            # 交换图块
            current_state[zero_pos[0]][zero_pos[1]], current_state[tile_r][tile_c] = \
                current_state[tile_r][tile_c], current_state[zero_pos[0]][zero_pos[1]]

            # 重新绘制拼图
            draw_puzzle(screen, current_state, tiles, rows, cols, tile_width, tile_height)
            pygame.display.flip()

            # 等待0.5秒
            time.sleep(0.5)
        
        # 保持窗口打开直到用户关闭
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False

    pygame.quit()
    sys.exit()

if __name__ == '__main__':
    main()