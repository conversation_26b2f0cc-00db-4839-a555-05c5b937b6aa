import pyautogui
from PIL import Image
import json
import imagehash
import numpy as np
import cv2

def capture_screen(region=None):
    """
    Captures a screenshot of the specified region.

    Args:
        region (tuple, optional): A tuple (left, top, width, height) 
                                  specifying the region to capture. 
                                  If None, captures the entire screen. 
                                  Defaults to None.

    Returns:
        PIL.Image.Image: The captured screenshot as a Pillow Image object.
    """
    if region:
        screenshot = pyautogui.screenshot(region=region)
    else:
        screenshot = pyautogui.screenshot()
    return screenshot


def load_reference_tiles(puzzle_id):
    """
    Loads the reference tile images for a specific puzzle.

    Args:
        puzzle_id (str): The ID of the puzzle.

    Returns:
        dict: A dictionary where keys are piece_number and values are Pillow Image objects.
    """
    with open('puzzle_metadata.json', 'r', encoding='utf-8') as f:
        puzzles = json.load(f)
    
    puzzle_info = next((p for p in puzzles if p['puzzle_id'] == puzzle_id), None)
    
    if not puzzle_info:
        raise ValueError(f"Puzzle with ID '{puzzle_id}' not found in metadata.")
        
    reference_tiles = {}
    for piece in puzzle_info['pieces']:
        try:
            img = Image.open(piece['path'])
            reference_tiles[piece['piece_number']] = img
        except FileNotFoundError:
            print(f"Warning: Image file not found for piece {piece['piece_number']} at path: {piece['path']}")
            # Optionally, handle this case by skipping or adding a placeholder
            pass
            
    return reference_tiles

def is_blank(image, threshold=10):
    """
    Check if a tile is blank (mostly one color).
    """
    extrema = image.convert("L").getextrema()
    return (extrema[1] - extrema[0]) < threshold

def match_tile(tile_image, reference_tiles, hamming_threshold=5, is_blank_threshold=10):
    """
    Matches a single tile image against a dictionary of reference tiles using Hamming distance.

    Args:
        tile_image (PIL.Image.Image): The tile image to match.
        reference_tiles (dict): A dictionary of reference tile images.
        hamming_threshold (int): The maximum Hamming distance to consider a match.
        is_blank_threshold (int): The threshold to use for the is_blank check.

    Returns:
        tuple: A tuple containing the best matching piece_number and the Hamming distance.
               Returns (0, 0) for a blank tile.
               Returns (None, float('inf')) if no match is found below the threshold.
    """
    if is_blank(tile_image, threshold=is_blank_threshold):
        return 0, 0

    tile_hash = imagehash.phash(tile_image)
    
    best_match = None
    min_distance = float('inf')
    
    # Pre-compute reference hashes if they are not already hashes
    # This is an optimization if reference_tiles contains images instead of hashes
    reference_hashes = {}
    for piece_number, ref_obj in reference_tiles.items():
        if isinstance(ref_obj, imagehash.ImageHash):
            reference_hashes[piece_number] = ref_obj
        else: # Assuming it's a PIL Image
            reference_hashes[piece_number] = imagehash.phash(ref_obj)

    for piece_number, ref_hash in reference_hashes.items():
        distance = tile_hash - ref_hash
        if distance < min_distance:
            min_distance = distance
            best_match = piece_number

    if min_distance <= hamming_threshold:
        return best_match, min_distance
    else:
        return None, min_distance

def get_puzzle_state(screenshot, size, reference_tiles, debug=False):
    """
    Generates the state matrix for the puzzle from a screenshot using various OpenCV strategies.

    Args:
        screenshot (PIL.Image.Image): The screenshot of the puzzle area.
        size (int): The size of the puzzle grid (e.g., 3 for 3x3).
        reference_tiles (dict): The dictionary of reference tile images.
        debug (bool): If True, save intermediate processing images.

    Returns:
        list: A 2D list representing the puzzle state.
    
    Raises:
        ValueError: If no strategy yields the correct number of contours.
    """
    open_cv_image = np.array(screenshot)
    open_cv_image = open_cv_image[:, :, ::-1].copy()
    gray = cv2.cvtColor(open_cv_image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    if debug:
        cv2.imwrite('debug_grayscale.png', gray)
        cv2.imwrite('debug_blurred.png', blurred)

    strategies = {
        'adaptive_mean': lambda img: cv2.adaptiveThreshold(img, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY_INV, 11, 2),
        'adaptive_gaussian': lambda img: cv2.adaptiveThreshold(img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2),
        'canny': lambda img: cv2.Canny(img, 50, 150)
    }

    tile_contours = []
    expected_contours = size * size

    for name, strategy_func in strategies.items():
        processed_img = strategy_func(blurred)
        if debug:
            cv2.imwrite(f'debug_{name}.png', processed_img)

        contours, _ = cv2.findContours(processed_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Dynamic area filtering
        total_area = screenshot.width * screenshot.height
        expected_tile_area = total_area / (size * size)
        min_area = expected_tile_area * 0.5  # 50% of expected area
        max_area = expected_tile_area * 1.5  # 150% of expected area

        current_strategy_contours = [c for c in contours if min_area < cv2.contourArea(c) < max_area]

        if len(current_strategy_contours) == expected_contours:
            print(f"Strategy '{name}' succeeded with {len(current_strategy_contours)} contours.")
            tile_contours = current_strategy_contours
            break
        else:
            print(f"Strategy '{name}' failed. Found {len(current_strategy_contours)} contours, expected {expected_contours}.")
    
    if not tile_contours:
        raise ValueError(f"All preprocessing strategies failed to find the expected {expected_contours} contours.")

    if debug:
        debug_contour_img = open_cv_image.copy()
        cv2.drawContours(debug_contour_img, tile_contours, -1, (0, 255, 0), 2)
        cv2.imwrite('debug_contours.png', debug_contour_img)

    bounding_boxes = [cv2.boundingRect(c) for c in tile_contours]
    
    y_tolerance = 10
    bounding_boxes.sort(key=lambda x: (round(x[1] / y_tolerance) * y_tolerance, x[0]))

    state = []
    row = []
    for i, (x, y, w, h) in enumerate(bounding_boxes):
        tile_image = screenshot.crop((x, y, x + w, y + h))
        piece_number, distance = match_tile(tile_image, reference_tiles)
        
        if piece_number is None:
            if debug:
                tile_image.save(f"debug_failed_match_{i}.png")
            raise ValueError(f"Could not find a match for tile at contour index {i} with min distance {distance}")
            
        row.append(piece_number)
        
        if len(row) == size:
            state.append(row)
            row = []
            
    if row: # Should not happen if contour count is correct
        state.append(row)

    if len(state) != size or any(len(r) != size for r in state):
         raise ValueError(f"The final state matrix does not have the correct dimensions. Expected {size}x{size}, but got {len(state)}x{len(state[0]) if state else 0}")

    return state


def format_state_for_solver(state_matrix):
    """
    Formats the puzzle state matrix for the solver.

    Args:
        state_matrix (list): A 2D list representing the puzzle state.

    Returns:
        str: A comma-separated string of the flattened state.
    """
    flattened_list = [str(item) for sublist in state_matrix for item in sublist]
    return ",".join(flattened_list)
