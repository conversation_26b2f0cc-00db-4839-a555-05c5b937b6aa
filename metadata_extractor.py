import os
import json
import re

def extract_metadata():
    root_dirs = ["3阶", "4阶", "5阶"]
    puzzles = {}

    for directory in root_dirs:
        if not os.path.isdir(directory):
            continue
            
        size = int(re.search(r'\d+', directory).group())
        
        for subdir, _, files in os.walk(directory):
            for file in files:
                if file.endswith(".png"):
                    match = re.match(r"(\d+)_(\d+)\.gsa0\.png", file)
                    if match:
                        puzzle_id = match.group(1)
                        piece_number = int(match.group(2))
                        path = os.path.join(subdir, file).replace("\\", "/")

                        if puzzle_id not in puzzles:
                            puzzles[puzzle_id] = {
                                "puzzle_id": puzzle_id,
                                "size": size,
                                "pieces": []
                            }
                        
                        puzzles[puzzle_id]["pieces"].append({
                            "piece_number": piece_number,
                            "path": path
                        })

    for puzzle_id in puzzles:
        puzzles[puzzle_id]["pieces"].sort(key=lambda p: p["piece_number"])

    result = list(puzzles.values())
    
    with open("puzzle_metadata.json", "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print("Successfully generated puzzle_metadata.json")

if __name__ == "__main__":
    extract_metadata()