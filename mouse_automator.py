import pyautogui
import time
import numpy as np

def get_tile_center(puzzle_region, tile_position, puzzle_size):
    """
    计算并返回给定图块中心的屏幕坐标。

    :param puzzle_region: (left, top, width, height) 元组，代表拼图在屏幕上的区域。
    :param tile_position: (row, col) 元组，代表图块在拼图网格中的位置。
    :param puzzle_size: 整数，代表拼图的尺寸 (例如 3x3 的拼图为 3)。
    :return: (x, y) 元组，代表图块中心的屏幕坐标。
    """
    left, top, width, height = puzzle_region
    row, col = tile_position
    
    tile_width = width / puzzle_size
    tile_height = height / puzzle_size
    
    # 计算图块中心的 x 坐标
    center_x = left + (col * tile_width) + (tile_width / 2)
    # 计算图块中心的 y 坐标
    center_y = top + (row * tile_height) + (tile_height / 2)
    
    return int(center_x), int(center_y)

def execute_move(puzzle_region, puzzle_size, current_state, move):
    """
    执行单步移动，点击相应图块，并返回新的拼图状态。

    :param puzzle_region: (left, top, width, height) 元组。
    :param puzzle_size: 整数，拼图尺寸。
    :param current_state: 表示当前拼图状态的 numpy 数组。
    :param move: 字符串 ('up', 'down', 'left', 'right')，表示要执行的移动。
    :return: 移动后新的拼图状态 numpy 数组。
    """
    # 查找空白图块 (0) 的位置
    zero_pos_tuple = np.where(current_state == 0)
    zero_pos = (zero_pos_tuple[0][0], zero_pos_tuple[1][0])
    
    # 根据移动方向确定要点击的图块位置
    move_offsets = {
        'up': (1, 0),
        'down': (-1, 0),
        'left': (0, 1),
        'right': (0, -1)
    }
    
    offset = move_offsets[move]
    target_tile_pos = (zero_pos[0] + offset[0], zero_pos[1] + offset[1])

    # 检查目标位置是否在边界内
    if not (0 <= target_tile_pos[0] < puzzle_size and 0 <= target_tile_pos[1] < puzzle_size):
        # 如果移动无效，则不执行任何操作并返回原始状态
        return current_state

    # 获取目标图块的中心坐标
    click_x, click_y = get_tile_center(puzzle_region, target_tile_pos, puzzle_size)
    
    # 模拟鼠标点击
    pyautogui.click(click_x, click_y)
    
    # 更新拼图状态
    new_state = np.copy(current_state)
    new_state[zero_pos] = new_state[target_tile_pos]
    new_state[target_tile_pos] = 0
    
    return new_state

def automate_solution(puzzle_region, puzzle_size, initial_state, solution_path):
    """
    根据给定的解法路径，自动化执行整个拼图过程。

    :param puzzle_region: (left, top, width, height) 元组。
    :param puzzle_size: 整数，拼图尺寸。
    :param initial_state: 初始拼图状态的 numpy 数组。
    :param solution_path: 包含移动指令 ('up', 'down', 'left', 'right') 的列表。
    """
    current_state = np.copy(initial_state)
    
    for move in solution_path:
        current_state = execute_move(puzzle_region, puzzle_size, current_state, move)
        # 等待一小段时间以便观察
        time.sleep(0.5)

if __name__ == '__main__':
    # 这是一个示例用法，需要根据实际情况进行调整
    
    # 假设拼图区域在屏幕上的 (100, 100) 位置，大小为 300x300 像素
    puzzle_region_example = (100, 100, 300, 300)
    
    # 拼图尺寸为 3x3
    puzzle_size_example = 3
    
    # 初始状态，0 代表空白图块
    initial_state_example = np.array([
        [1, 2, 3],
        [4, 0, 5],
        [7, 8, 6]
    ])
    
    # 一个简单的解法路径
    solution_path_example = ['right', 'down']
    
    print("开始自动执行拼图解法...")
    print(f"初始状态:\n{initial_state_example}")
    
    # 在开始前留出几秒钟切换到拼图窗口
    time.sleep(3)
    
    automate_solution(puzzle_region_example, puzzle_size_example, initial_state_example, solution_path_example)
    
    print("解法执行完毕。")
