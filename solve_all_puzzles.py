import json
import subprocess
import os

def get_puzzle_ids_and_sizes():
    """从 puzzle_metadata.json 加载所有拼图的 ID 和尺寸"""
    try:
        with open('puzzle_metadata.json', 'r', encoding='utf-8') as f:
            puzzles = json.load(f)
        return {p['puzzle_id']: p['size'] for p in puzzles}
    except FileNotFoundError:
        print("错误: puzzle_metadata.json 未找到。")
        return {}

def solve_all(puzzle_data):
    """为所有拼图生成解法"""
    all_solutions = {}
    for puzzle_id, size in puzzle_data.items():
        print(f"正在为拼图 {puzzle_id} (尺寸: {size}x{size}) 生成解法...")

        # 1. 生成乱序状态
        try:
            scrambled_state_result = subprocess.run(
                ['python', 'generate_scrambled_state.py', '--size', str(size)],
                capture_output=True, text=True, check=True, encoding='utf-8'
            )
            initial_state = scrambled_state_result.stdout.strip()
            print(f"  - 成功生成初始状态: {initial_state}")
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            print(f"  - 错误: 生成初始状态失败 - {e}")
            continue

        # 2. 运行解法器
        try:
            subprocess.run(
                ['python', 'solver.py', '--puzzle-id', puzzle_id, '--initial-state', initial_state],
                check=True, capture_output=True, text=True, encoding='utf-8'
            )
            solution_filename = f"solution_{puzzle_id}.json"
            print(f"  - 成功解决拼图，解法已保存至 {solution_filename}")

            # 3. (可选) 将单个解法加载到总汇中
            if os.path.exists(solution_filename):
                with open(solution_filename, 'r', encoding='utf-8') as f:
                    all_solutions[puzzle_id] = json.load(f)

        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            print(f"  - 错误: 解决拼图 {puzzle_id} 失败 - {e}")
            if e.stderr:
                print(f"  - 解法器错误输出:\n{e.stderr}")
            continue
    
    return all_solutions

def main():
    """主函数"""
    puzzle_data = get_puzzle_ids_and_sizes()
    if not puzzle_data:
        return

    all_solutions = solve_all(puzzle_data)

    # 4. 将所有解法汇总到一个文件中
    if all_solutions:
        with open('all_solutions.json', 'w', encoding='utf-8') as f:
            json.dump(all_solutions, f, indent=2, ensure_ascii=False)
        print("\n所有解法已成功汇总到 all_solutions.json")

if __name__ == "__main__":
    main()