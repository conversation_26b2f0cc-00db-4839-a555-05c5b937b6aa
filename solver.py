import json
import argparse
import heapq
import sys
import io

def _generate_target_state(size):
    """Generates the target state for a given puzzle size."""
    target = [[0] * size for _ in range(size)]
    count = 1
    for r in range(size):
        for c in range(size):
            if count == size * size:
                target[r][c] = 0
            else:
                target[r][c] = count
                count += 1
    return target

class PuzzleNode:
    """Represents a node in the A* search tree."""
    def __init__(self, state, target_state, parent=None, move=None, g=0):
        self.state = state
        self.parent = parent
        self.move = move
        self.g = g
        self.h = self.calculate_manhattan_distance(target_state)
        self.f = self.g + self.h

    def __lt__(self, other):
        return self.f < other.f

    def calculate_manhattan_distance(self, target_state):
        """Calculates the Manhattan distance heuristic."""
        distance = 0
        size = len(self.state)
        
        # Create a map of value to its target position for faster lookup
        target_pos = {val: (r, c) for r, row in enumerate(target_state) for c, val in enumerate(row)}

        for r in range(size):
            for c in range(size):
                val = self.state[r][c]
                if val != 0:
                    target_r, target_c = target_pos[val]
                    distance += abs(r - target_r) + abs(c - target_c)
        return distance

    def get_neighbors(self, target_state):
        """Generates neighbor states by moving the blank tile."""
        neighbors = []
        size = len(self.state)
        
        # Find the position of the blank tile (0)
        blank_pos = None
        for r in range(size):
            for c in range(size):
                if self.state[r][c] == 0:
                    blank_pos = (r, c)
                    break
            if blank_pos:
                break
        
        r, c = blank_pos
        moves = {'up': (-1, 0), 'down': (1, 0), 'left': (0, -1), 'right': (0, 1)}

        for move, (dr, dc) in moves.items():
            nr, nc = r + dr, c + dc
            if 0 <= nr < size and 0 <= nc < size:
                new_state = [row[:] for row in self.state]
                new_state[r][c], new_state[nr][nc] = new_state[nr][nc], new_state[r][c]
                neighbors.append(PuzzleNode(new_state, target_state, parent=self, move=move, g=self.g + 1))
        return neighbors

def solve_puzzle(initial_state, puzzle_id):
    """Solves the puzzle using A* algorithm."""
    size = len(initial_state)
    target_state = _generate_target_state(size)
    
    start_node = PuzzleNode(initial_state, target_state)
    if start_node.state == target_state:
        return {"puzzle_id": puzzle_id, "solution_path": [], "steps": 0}

    open_list = [start_node]
    closed_set = set()

    while open_list:
        current_node = heapq.heappop(open_list)

        if current_node.state == target_state:
            path = []
            steps = 0
            node = current_node
            while node.parent:
                path.append(node.move)
                node = node.parent
                steps += 1
            path.reverse()
            return {"puzzle_id": str(puzzle_id), "solution_path": path, "steps": steps}

        closed_set.add(tuple(map(tuple, current_node.state)))

        for neighbor in current_node.get_neighbors(target_state):
            if tuple(map(tuple, neighbor.state)) in closed_set:
                continue
            
            # Check if neighbor is in open_list and if the new path is better
            in_open_list = False
            for i, node in enumerate(open_list):
                if node.state == neighbor.state:
                    in_open_list = True
                    if neighbor.g < node.g:
                        open_list[i] = neighbor
                        heapq.heapify(open_list)
                    break
            
            if not in_open_list:
                heapq.heappush(open_list, neighbor)

    return None # No solution found

def main():
    """Main function to parse arguments and solve the puzzle."""
    # Set output encoding to UTF-8
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

    parser = argparse.ArgumentParser(description="Solve a sliding puzzle using A* algorithm.")
    parser.add_argument("--puzzle-id", required=True, help="The ID of the puzzle.")
    parser.add_argument("--initial-state", required=True, help="Comma-separated list of initial tile positions.")
    
    args = parser.parse_args()
    
    try:
        with open('puzzle_metadata.json', 'r', encoding='utf-8') as f:
            puzzles = json.load(f)
    except FileNotFoundError:
        print("Error: puzzle_metadata.json not found.")
        return

    puzzle_info = next((p for p in puzzles if p["puzzle_id"] == args.puzzle_id), None)
    if not puzzle_info:
        print(f"Error: Puzzle with ID '{args.puzzle_id}' not found in metadata.")
        return
        
    size = puzzle_info["size"]
    try:
        initial_tiles = [int(x) for x in args.initial_state.split(',')]
    except ValueError:
        print("Error: Invalid format for initial-state. Must be comma-separated integers.")
        return

    if len(initial_tiles) != size * size:
        print(f"Error: Initial state must contain {size*size} tiles for a {size}x{size} puzzle.")
        return

    initial_state = [initial_tiles[i:i+size] for i in range(0, len(initial_tiles), size)]
    
    solution = solve_puzzle(initial_state, args.puzzle_id)
    
    output_filename = f"solution_{args.puzzle_id}.json"
    if solution:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(solution, f, indent=2, ensure_ascii=False)
    else:
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump({"puzzle_id": args.puzzle_id, "error": "No solution found."}, f, indent=2, ensure_ascii=False)
    print(f"Solution saved to {output_filename}")

if __name__ == "__main__":
    main()