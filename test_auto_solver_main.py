import unittest
from unittest.mock import patch, mock_open, MagicMock, call
import json
import subprocess
import sys
import io

# Mock pyautogui before it's imported by the module we're testing
# This is necessary because pyautogui might not be available in the test environment
sys.modules['pyautogui'] = MagicMock()

from auto_solver_main import get_puzzle_region_from_user, main

class TestAutoSolverMain(unittest.TestCase):

    @patch('auto_solver_main.pyautogui.position')
    @patch('builtins.input')
    def test_get_puzzle_region_from_user_valid(self, mock_input, mock_position):
        """
        测试 get_puzzle_region_from_user 函数在有效输入下的情况
        """
        # 模拟用户输入和鼠标位置
        mock_input.side_effect = ["", ""] # Simulate user pressing Enter
        mock_position.side_effect = [
            MagicMock(x=100, y=100), # Top-left
            MagicMock(x=400, y=400)  # Bottom-right
        ]
        
        expected_region = (100, 100, 300, 300)
        result = get_puzzle_region_from_user()
        
        self.assertEqual(result, expected_region)
        self.assertEqual(mock_input.call_count, 2)

    @patch('auto_solver_main.pyautogui.position')
    @patch('builtins.input')
    def test_get_puzzle_region_from_user_invalid_width(self, mock_input, mock_position):
        """
        测试 get_puzzle_region_from_user 函数在宽度无效下的情况
        """
        mock_input.side_effect = ["", ""]
        mock_position.side_effect = [
            MagicMock(x=400, y=100), # Top-left
            MagicMock(x=100, y=400)  # Bottom-right (x is smaller)
        ]
        
        result = get_puzzle_region_from_user()
        self.assertIsNone(result)

    @patch('auto_solver_main.capture_screen')
    @patch('auto_solver_main.load_reference_tiles')
    @patch('auto_solver_main.get_puzzle_state')
    @patch('auto_solver_main.format_state_for_solver')
    @patch('auto_solver_main.subprocess.run')
    @patch('auto_solver_main.automate_solution')
    @patch('auto_solver_main.get_puzzle_region_from_user')
    @patch('builtins.input')
    @patch('auto_solver_main.time.sleep', return_value=None)
    @patch('auto_solver_main.configure_io_streams') # Patch the new function
    @patch('sys.stdout', new_callable=io.StringIO) # Still need to capture output
    def test_main_happy_path(self, mock_stdout, mock_configure_io, mock_sleep, mock_input, mock_get_region, mock_automate, mock_subprocess, mock_format_state, mock_get_state, mock_load_tiles, mock_capture):
        """
        测试 main 函数的完整成功路径
        """
        # --- Mocks Setup ---
        # 1. 用户输入
        mock_input.return_value = "11" # Puzzle ID
        mock_get_region.return_value = (0, 0, 300, 300)

        # 2. 文件系统 I/O
        mock_metadata = [{"puzzle_id": "11", "size": 3}]
        mock_solution = {"solution_path": ["up", "down"], "steps": 2}
        
        # Mock 'open' to handle two different files
        m_open = mock_open()
        m_open.side_effect = [
            mock_open(read_data=json.dumps(mock_metadata)).return_value,
            mock_open(read_data=json.dumps(mock_solution)).return_value
        ]

        # 3. 图像识别
        mock_capture.return_value = "fake_screenshot"
        mock_load_tiles.return_value = {"1": "tile_1_img"}
        mock_get_state.return_value = [[1, 2, 3], [4, 5, 6], [7, 8, 0]]
        mock_format_state.return_value = "1,2,3,4,5,6,7,8,0"

        # 4. 子进程
        mock_subprocess.return_value = MagicMock(check=True)

        # --- Execution ---
        with patch('builtins.open', m_open):
            main()
        
        # Assert that our IO configuration function was not called
        mock_configure_io.assert_called_once()
        
        # --- Assertions ---
        # 验证文件读取
        m_open.assert_any_call('puzzle_metadata.json', 'r', encoding='utf-8')
        m_open.assert_any_call('solution_11.json', 'r', encoding='utf-8')

        # 验证图像识别函数调用
        mock_capture.assert_called_once_with(region=(0, 0, 300, 300))
        mock_load_tiles.assert_called_once_with("11")
        mock_get_state.assert_called_once_with("fake_screenshot", 3, {"1": "tile_1_img"})
        mock_format_state.assert_called_once_with([[1, 2, 3], [4, 5, 6], [7, 8, 0]])

        # 验证解法器调用
        mock_subprocess.assert_called_once_with(
            ['python', 'solver.py', '--puzzle-id', '11', '--initial-state', '1,2,3,4,5,6,7,8,0'],
            check=True, capture_output=True, text=True, encoding='utf-8'
        )

        # 验证鼠标自动化调用
        mock_automate.assert_called_once_with(
            (0, 0, 300, 300), 3, [[1, 2, 3], [4, 5, 6], [7, 8, 0]], ["up", "down"]
        )
        
        # 验证输出
        output = mock_stdout.getvalue()
        self.assertIn("成功生成解法，共 2 步。", output)
        self.assertIn("拼图已自动完成！", output)

    @patch('builtins.input', return_value="99") # Non-existent ID
    @patch('auto_solver_main.configure_io_streams')
    @patch('sys.stdout', new_callable=io.StringIO)
    def test_main_puzzle_id_not_found(self, mock_stdout, mock_configure_io, mock_input):
        """
        测试当 puzzle_id 在元数据中找不到时 main 函数的行为
        """
        mock_metadata = [{"puzzle_id": "11", "size": 3}]
        m_open = mock_open(read_data=json.dumps(mock_metadata))
        
        with patch('builtins.open', m_open):
            main()
        
        output = mock_stdout.getvalue()
        self.assertIn("错误: 未在 puzzle_metadata.json 中找到 ID 为 99 的拼图。", output)

    @patch('builtins.input', return_value="11") # Mock input to prevent EOFError
    @patch('auto_solver_main.configure_io_streams')
    @patch('sys.stdout', new_callable=io.StringIO)
    def test_main_metadata_file_not_found(self, mock_stdout, mock_configure_io, mock_input):
        """
        测试当 puzzle_metadata.json 文件不存在时 main 函数的行为
        """
        # Simulate FileNotFoundError by patching open to raise it
        with patch('builtins.open', mock_open()) as m_open:
            m_open.side_effect = FileNotFoundError
            main()

        output = mock_stdout.getvalue()
        self.assertIn("错误: puzzle_metadata.json 未找到。", output)
        mock_configure_io.assert_called_once()

if __name__ == '__main__':
    unittest.main(argv=['first-arg-is-ignored'], exit=False)