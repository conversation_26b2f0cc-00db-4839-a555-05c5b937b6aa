import unittest
from PIL import Image
import imagehash
import numpy as np
from image_recognizer import match_tile, is_blank

class TestImageRecognizer(unittest.TestCase):

    def setUp(self):
        # Create some dummy images for testing
        self.img1 = Image.new('RGB', (100, 100), color = 'red')
        self.img2 = Image.new('RGB', (100, 100), color = 'blue')
        self.img3 = Image.new('RGB', (100, 100), color = (250, 0, 0)) # very similar to red
        self.blank_img = Image.new('RGB', (100, 100), color = 'white')

        self.ref_tiles = {
            1: self.img1,
            2: self.img2,
        }

    def test_is_blank(self):
        self.assertTrue(is_blank(self.blank_img))
        self.assertFalse(is_blank(self.img1, threshold=0))

    def test_match_tile_exact_match(self):
        piece_number, distance = match_tile(self.img1, self.ref_tiles, is_blank_threshold=0)
        self.assertEqual(piece_number, 1)
        self.assertEqual(distance, 0)

    def test_match_tile_close_match(self):
        piece_number, distance = match_tile(self.img3, self.ref_tiles, is_blank_threshold=0)
        self.assertEqual(piece_number, 1)
        self.assertLess(distance, 5)

    def test_match_tile_no_match(self):
        # Create a completely different image (random noise)
        np_img = np.random.randint(0, 255, (100, 100, 3), dtype='uint8')
        img4 = Image.fromarray(np_img)
        piece_number, distance = match_tile(img4, self.ref_tiles, hamming_threshold=2, is_blank_threshold=0)
        self.assertIsNone(piece_number)

    def test_match_tile_blank_tile(self):
        piece_number, distance = match_tile(self.blank_img, self.ref_tiles, is_blank_threshold=10)
        self.assertEqual(piece_number, 0)
        self.assertEqual(distance, 0)

if __name__ == '__main__':
    unittest.main()